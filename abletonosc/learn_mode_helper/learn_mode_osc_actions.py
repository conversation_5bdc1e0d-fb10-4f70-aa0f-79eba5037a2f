"""
Actions OSC pour le mode learn
Gère les actions déclenchées par les messages OSC (start_learn_listen, set_learn_slot_value, etc.)
"""

import json


def start_learn_listen(self, params):
    """Démarre l'écoute des paramètres pour un slot d'apprentissage"""
    self.logger.info(f"=== start_learn_listen appelé avec params: {params} ===")
    learn_slot = int(params[0])
    param_type = int(params[1])
    track_index = int(params[2])
    learned_track = None
    learned_device = None
    learned_chain = None
    chain_path = None
    param_index = None

    self.logger.info(f"Parsing: learn_slot={learn_slot}, param_type={param_type}, track_index={track_index}")

    try:
        # Gestion spéciale pour track_index = -1 (locked track)
        if track_index == -1 and self.manager.trackLock and self.manager.lockedTrack:
            learned_track = self.manager.lockedTrack
        # Gestion spéciale pour track_index = -2 (ChainTrackIndex) - utilisé pour les devices et chains
        elif track_index == -2:
            # Pour les devices et chains, on utilise la piste sélectionnée
            learned_track = self.song.view.selected_track
            if not learned_track:
                self.logger.error("Aucune piste sélectionnée pour track_index = -2")
                return
        else:
            # Comportement existant pour les autres cas
            all_tracks = self.manager.get_visible_tracks()
            if 0 <= track_index < len(all_tracks):
                learned_track = all_tracks[track_index]

        # Configuration spéciale pour le volume et le pan de chaîne (type 5 et 6)
        if param_type in [5, 6, 9, 10]:  # Ajout des types 9 et 10 ici
            if isinstance(params[3], list):  # Si on reçoit un chemin sous forme de liste
                chain_path = params[3]
                self.logger.debug(f"Chain path reçu sous forme de liste: {chain_path}")
                selected_track = self.song.view.selected_track

                if not selected_track:
                    self.logger.warning("Aucune piste sélectionnée")
                    return

                learned_chain = self._get_chain_by_path(chain_path)

                if not learned_chain:
                    self.logger.warning(f"Impossible de trouver la chaîne avec le chemin: {chain_path}")
                    return

                # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                learned_track = selected_track
            elif isinstance(params[3], str) and params[3].startswith("[") and params[3].endswith("]"):  # JSON string
                # Essayer de parser le JSON
                try:
                    chain_path = json.loads(params[3])
                    self.logger.debug(f"Chain path reçu sous forme de JSON: {chain_path}")
                    selected_track = self.song.view.selected_track

                    if not selected_track:
                        self.logger.warning("Aucune piste sélectionnée")
                        return

                    learned_chain = self._get_chain_by_path(chain_path)

                    if not learned_chain:
                        self.logger.warning(f"Impossible de trouver la chaîne avec le chemin JSON: {chain_path}")
                        return

                    # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                    learned_track = selected_track
                except json.JSONDecodeError as e:
                    self.logger.error(f"Erreur lors du décodage JSON du chemin de chaîne: {e}")
                    return
            else:  # Ancien format (chaîne de caractères)
                chain_id = params[3]  # Le chain_id est maintenant fourni directement (format "rack_idx_chain_idx" ou "-1")
                self.logger.debug(f"Chain ID reçu sous forme de chaîne: {chain_id}")
                selected_track = self.song.view.selected_track
                if not selected_track:
                    self.logger.warning("Aucune piste sélectionnée")
                    return

                # Cas spécial pour chain_id = "-1" : utiliser la selected_chain
                if chain_id == "-1":
                    selected_device = selected_track.view.selected_device
                    if not selected_device or not hasattr(selected_device, 'chains'):
                        self.logger.warning("Pas de rack sélectionné")
                        return

                    selected_chain = selected_device.view.selected_chain
                    if not selected_chain:
                        self.logger.warning("Pas de chaîne sélectionnée")
                        return

                    learned_chain = selected_chain
                    # Récupérer le vrai chemin de chaîne pour le stockage
                    chain_path = self._get_chain_path(selected_chain)
                else:
                    # Essayer de convertir en chemin
                    try:
                        rack_idx, chain_idx = map(int, chain_id.split('_'))
                        chain_path = [rack_idx, chain_idx]
                        learned_chain = self._get_chain_by_path(chain_path)
                    except Exception as e:
                        self.logger.error(f"Erreur lors de la conversion du chain_id en chemin: {e}")
                        return

                # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                learned_track = selected_track

        # Configuration spéciale pour les paramètres de device (type 4)
        elif param_type == 4:
            device_index = int(params[3])
            param_index = int(params[4])
            self.logger.info(f"Device param: device_index={device_index}, param_index={param_index}")

            # Utiliser le lockedDevice si device_index est -4
            if device_index == -4 and self.manager.deviceLock and self.manager.lockedDevice:
                learned_device = self.manager.lockedDevice
                self.logger.info(f"Using locked device: {learned_device.name}")
                # Utiliser la track déjà déterminée ou trouver la track du device
                if not learned_track:
                    learned_track = self._find_device_or_chain_track(learned_device)
                    self.logger.info(f"Found track for locked device: {learned_track.name if learned_track else None}")
            else:
                # Utiliser la track déjà déterminée (devrait être selected_track pour track_index = -2)
                if not learned_track:
                    learned_track = self.song.view.selected_track
                    self.logger.info(f"Using selected track: {learned_track.name if learned_track else None}")

                if learned_track:
                    visible_devices = self.manager.get_visible_devices(learned_track)
                    self.logger.info(f"Found {len(visible_devices)} visible devices on track {learned_track.name}")
                    if 0 <= device_index < len(visible_devices):
                        learned_device = visible_devices[device_index]
                        self.logger.info(f"Selected device: {learned_device.name}")
                    else:
                        self.logger.error(f"Device index {device_index} out of range (0-{len(visible_devices)-1})")

            if not learned_device:
                raise Exception(f"Device not found: device_index={device_index}, track={learned_track.name if learned_track else None}")

    except Exception as e:
        self.logger.error(f"Error getting track/device: {e}")
        return

    # Vérifier que learned_track est défini
    if not learned_track:
        self.logger.error("Impossible de déterminer la piste associée")
        return

    # CRUCIAL: Nettoyer complètement le slot avant de le réassigner
    self._clean_slot_listeners(learn_slot)
    
    # Vérification des doublons (après nettoyage du slot actuel)
    send_index = int(params[3]) if param_type == 3 else None
    self._check_duplicates(learn_slot, param_type, learned_track,
                         chain=learned_chain,
                         device=learned_device,
                         param_index=param_index,
                         send_index=send_index)

    # Configuration du slot
    track_name = f"track{learn_slot}"
    self.learn_slots[learn_slot]["track"] = learned_track
    self.learn_slots[learn_slot]["param_type"] = param_type
    
    # Stocker param_index pour les devices APRÈS le nettoyage
    if param_type == 4 and learned_device:
        self.learn_slots[learn_slot]["device"] = learned_device
        self.learn_slots[learn_slot]["param_index"] = param_index

    # Configuration des listeners selon le type de paramètre
    if param_type == 1:
        self._setup_volume_listener(learn_slot, track_name, learned_track)
    elif param_type == 2:
        self._setup_pan_listener(learn_slot, track_name, learned_track)
    elif param_type == 3:
        self._setup_send_listener(learn_slot, track_name, learned_track, send_index)
        self.learn_slots[learn_slot]["send_index"] = send_index
    elif param_type == 4 and learned_device:
        # device et param_index déjà stockés plus haut
        self.logger.info(f"Setting up device listeners for slot {learn_slot}, device {learned_device.name}, param {param_index}")



        self._setup_device_listeners(learn_slot, track_name, learned_device, learned_track)
        if param_index < len(learned_device.parameters):
            self.logger.info(f"Setting up parameter listener for param {param_index}/{len(learned_device.parameters)}")
            self._setup_device_parameter_listener(learn_slot, track_name, learned_device, param_index)
        else:
            self.logger.error(f"Param index {param_index} out of range for device {learned_device.name} (has {len(learned_device.parameters)} params)")
    elif param_type == 5 and learned_chain:
        self.learn_slots[learn_slot]["chain"] = learned_chain
        self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
        self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
        self._setup_chain_volume_listener(learn_slot, track_name, learned_chain)
    elif param_type == 6 and learned_chain:
        self.learn_slots[learn_slot]["chain"] = learned_chain
        self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
        self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
        self._setup_chain_pan_listener(learn_slot, track_name, learned_chain)
    elif param_type == 7 and learned_track and learned_track != self.song.master_track:  # Mute
        self._setup_mute_listener(learn_slot, track_name, learned_track)
    elif param_type == 8 and learned_track and learned_track != self.song.master_track:  # Solo
        self._setup_solo_listener(learn_slot, track_name, learned_track)
    elif param_type == 9 and learned_chain:  # Chain Mute
        self.learn_slots[learn_slot]["chain"] = learned_chain
        self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
        self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
        self._setup_chain_mute_listener(learn_slot, track_name, learned_chain)
    elif param_type == 10 and learned_chain:  # Chain Solo
        self.learn_slots[learn_slot]["chain"] = learned_chain
        self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
        self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
        self._setup_chain_solo_listener(learn_slot, track_name, learned_chain)

    # Vérifier que track_to_monitor n'est pas None avant de configurer les listeners
    if learned_track:
        # Configuration des listeners de base pour la piste
        track_to_monitor = learned_track
        self._setup_track_listeners(learn_slot, track_name, track_to_monitor)

        # Envoi des références initiales
        self.send_learn_slot_reference(learn_slot)

        # Log du nombre total de listeners après configuration
        self.logger.info(f"Slot {learn_slot} configuré. Nombre total de listeners: {len(self.learn_listeners)}")
        
        # Debug: afficher l'état des listeners
        self._debug_listeners_state()
    else:
        self.logger.error(f"Impossible de configurer les listeners pour le slot {learn_slot} : piste non valide")


def select_learn_slot_element(self, params):
    """Sélectionne l'élément associé à un slot d'apprentissage"""
    learn_slot = int(params[0])
    slot_info = self.learn_slots.get(learn_slot, {})

    track = slot_info.get("track")
    device = slot_info.get("device")

    if track:
        # Sélectionner la piste
        self.song.view.selected_track = track
    if device:
        # Sélectionner le device
        self.song.view.select_device(device)


def stop_learn_listen(self):
    """Arrête l'écoute de tous les slots d'apprentissage"""
    self.logger.info("Stopping learn listen")
    for learn_slot in range(32):
        track = self.learn_slots[learn_slot].get("track")
        device = self.learn_slots[learn_slot].get("device")
        chain = self.learn_slots[learn_slot].get("chain")
        param_type = self.learn_slots[learn_slot].get("param_type")
        track_name = f"track{learn_slot}"

        if track and param_type:
            try:
                if param_type == 1:  # Volume
                    if (track_name, "volume") in self.learn_listeners:
                        track.mixer_device.volume.remove_value_listener(self.learn_listeners[(track_name, "volume")])
                        del self.learn_listeners[(track_name, "volume")]

                elif param_type == 2:  # Pan
                    if (track_name, "panning") in self.learn_listeners:
                        track.mixer_device.panning.remove_value_listener(self.learn_listeners[(track_name, "panning")])
                        del self.learn_listeners[(track_name, "panning")]

                elif param_type == 3:  # Send
                    send_index = self.learn_slots[learn_slot].get("send_index")
                    if send_index is not None and (track_name, f"send_{send_index}") in self.learn_listeners:
                        track.mixer_device.sends[send_index].remove_value_listener(
                            self.learn_listeners[(track_name, f"send_{send_index}")])
                        del self.learn_listeners[(track_name, f"send_{send_index}")]

                elif param_type == 4:  # Device Parameter
                    if device:
                        for key in list(self.learn_listeners.keys()):
                            if key.startswith(f"learn_{track_name}_device_param_"):
                                try:
                                    param_index = int(key.split("_")[-1])
                                    if param_index < len(device.parameters):
                                        device.parameters[param_index].remove_value_listener(
                                            self.learn_listeners[key])
                                        del self.learn_listeners[key]
                                except Exception as e:
                                    self.logger.error(f"Error removing device parameter listener: {e}")

                # ... (continuer avec les autres types de paramètres)

            except Exception as e:
                self.logger.error(f"Erreur lors de l'arrêt des listeners pour le slot {learn_slot}: {e}")

        # Supprimer les listeners de base
        try:
            if f"learn_{track_name}_color" in self.learn_listeners:
                if track:
                    track.remove_color_listener(self.learn_listeners[f"learn_{track_name}_color"])
                del self.learn_listeners[f"learn_{track_name}_color"]

            if f"learn_{track_name}_name" in self.learn_listeners:
                if track:
                    track.remove_name_listener(self.learn_listeners[f"learn_{track_name}_name"])
                del self.learn_listeners[f"learn_{track_name}_name"]
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression des listeners de base: {e}")

        # Réinitialiser le slot
        self.learn_slots[learn_slot] = {
            "track": None,
            "device": None,
            "param_type": None,
            "param_index": None
        }

    self.logger.info("Learn listen arrêté pour tous les slots")
